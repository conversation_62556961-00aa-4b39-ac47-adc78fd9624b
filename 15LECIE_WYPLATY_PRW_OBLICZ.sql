--------------------------------------------------------
--  DDL for Procedure 15LECIE_WYPLATY_PRW_OBLICZ
--------------------------------------------------------
set define off;

  CREATE OR REPLACE PROCEDURE "SUKCES"."15LECIE_WYPLATY_PRW_OBLICZ" 
AS
  v_Prowizje         NUMBER(7,2) := 0;
  v_Prowizje_ofe     NUMBER(7,2) := 0;
  v_Prowizje_INTEL   NUMBER(7,2) := 0;  
  v_<PERSON><PERSON>z<PERSON>_dalszor NUMBER(7,2) := 0;

BEGIN


  FOR v_Prac IN (SELECT DISTINCT P.ID FROM PRACOWNICY P, HISTORIE_PRAC H, POZYCJE POZ WHERE P.ID = H.PRA_ID AND POZ.ID = H.poz_id AND POZ.PIO_ID<=2 AND WYSOKOSC>3) LOOP


    for v_mr in ( 

    SELECT distinct rok_w as rok, mies_w as miesiac
      FROM Prowizje_Prac
      WHERE Prac_ID = v_Prac.ID
      AND Rok_Obliczen < 2
      AND rok_w*12+mies_w < 2012*12+4
    UNION
    SELECT distinct rok_w as rok, mies_w as miesiac
      FROM OFE_Prowizje_Prac
      WHERE Prac_ID = v_Prac.ID
      AND rok_w*12+mies_w < 2012*12+4
    UNION
    SELECT distinct rok_w as rok, mies_w as miesiac
      FROM INTEL_Prowizje_Prac
      WHERE Prac_ID = v_Prac.ID
      AND rok_w*12+mies_w < 2012*12+4
    UNION
    SELECT distinct rok_wyplaty as rok, mies_wyplaty as miesiac
      FROM Prowizje_Prac
      WHERE Prac_ID = v_Prac.ID
      AND rok_obliczen > 1
      AND rok_wyplaty*12+mies_wyplaty < 2012*12+4
    order by 1,2 ) loop


    --PIERWSZOROCZNE
    SELECT nvl(SUM(Kwota_Prowizji),0) INTO V_Prowizje
      FROM Prowizje_Prac
      WHERE Prac_ID = v_Prac.ID
      AND Rok_Obliczen < 2
      AND Rok_W = v_mr.Rok
      AND Mies_W = v_mr.Miesiac;

    --OFE
    SELECT nvl(SUM(Kwota_Prowizji),0) INTO V_Prowizje_OFE
      FROM OFE_Prowizje_Prac
      WHERE Prac_ID = v_Prac.ID
      AND Rok_W = v_mr.Rok
      AND Mies_W = v_mr.Miesiac;

    --INTEL
    SELECT nvl(SUM(Kwota_Prowizji),0) INTO V_Prowizje_INTEL
      FROM INTEL_Prowizje_Prac
      WHERE Prac_ID = v_Prac.ID
      AND Rok_W = v_mr.Rok
      AND Mies_W = v_mr.Miesiac;

    --DALSZOLETNIE DLA SERWISU CO MIESIAC... LUB CO STYCZEN DLA DYSTRYBUCJI     
    SELECT nvl(SUM(kwota_prowizji),0) INTO V_Prowizje_dalszor
      FROM prowizje_prac
     WHERE prac_id = v_Prac.ID
     AND rok_wyplaty = v_mr.Rok
     AND mies_wyplaty = v_mr.Miesiac
     AND rok_obliczen > 1;   


      INSERT INTO "15LECIE_WYPLATY_PRW_WP"(Pra_ID,   Rok,  Miesiac,  Prowizje,  Prowizje_ofe,  Prowizje_intel,  Prowizje_dalszor, razem)
                                       VALUES(v_Prac.ID,v_mr.Rok,v_mr.Miesiac,v_Prowizje,v_Prowizje_ofe,v_Prowizje_intel,v_Prowizje_dalszor,v_Prowizje+v_Prowizje_ofe+v_Prowizje_intel+v_Prowizje_dalszor);

    END LOOP;

  END LOOP;


COMMIT;
END;

/
