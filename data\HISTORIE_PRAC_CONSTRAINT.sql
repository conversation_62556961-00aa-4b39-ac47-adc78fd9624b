--------------------------------------------------------
--  Constraints for Table HISTORIE_PRAC
--------------------------------------------------------

  ALTER TABLE "SUKCES"."HISTORIE_PRAC" MODIFY ("ID" NOT NULL ENABLE);
 
  ALTER TABLE "SUKCES"."HISTORIE_PRAC" MODIFY ("SZEF_ID" NOT NULL ENABLE);
 
  ALTER TABLE "SUKCES"."HISTORIE_PRAC" MODIFY ("OD" NOT NULL ENABLE);
 
  ALTER TABLE "SUKCES"."HISTORIE_PRAC" MODIFY ("LOGIN" NOT NULL ENABLE);
 
  ALTER TABLE "SUKCES"."HISTORIE_PRAC" MODIFY ("KIEDY" NOT NULL ENABLE);
 
  ALTER TABLE "SUKCES"."HISTORIE_PRAC" MODIFY ("PRA_ID" NOT NULL ENABLE);
 
  ALTER TABLE "SUKCES"."HISTORIE_PRAC" MODIFY ("POZ_ID" NOT NULL ENABLE);
 
  ALTER TABLE "SUKCES"."HISTORIE_PRAC" MODIFY ("POZ_PIO_ID" NOT NULL ENABLE);
  GRANT DELETE, INSERT, SELECT, UPDATE ON "SUKCES"."HISTORIE_PRAC" TO "TIP_ADMIN";
 
  GRANT DELETE, INSERT, SELECT, UPDATE ON "SUKCES"."HISTORIE_PRAC" TO "TIP_LATA_NA_MENU";
 
  GRANT DELETE, INSERT, SELECT, UPDATE ON "SUKCES"."HISTORIE_PRAC" TO "TIP_PRACOWNIK";
