--------------------------------------------------------
--  DDL for Function OPIEKA_CZYJA_ID
--------------------------------------------------------

  CREATE OR REPLACE FUNCTION "SUKCES"."OPIEKA_CZYJA_ID" 
(p_rok_umowy number,p_umowa_id umowy.id%type,p_data date)
  return number is
kto varchar2(5) := 0;
begin
 -- 1 rok
 if p_rok_umowy <=1 then
   -- przeszukaj wszystkie odrzuc te w ktorych juz nie pracuje
   for v_opi in (select id, pra_id, od,opi_do from opieka where pio_id = 1 and umo_id = p_umowa_id order by 3)  loop
     if TRUNC(v_opi.od,'MM') <= p_data 
        and  (v_opi.opi_do >= ADD_MONTHS(trunc(p_data,'MM')-1,1) 
	or v_opi.opi_do IS NULL)
       then return (v_opi.id); 
     end if;
   end loop;
   -- znajdz ostatniego opiekuna niealeznie od tego czy pracuje.
   --moze byc serwis jako sprzedajacy,to wtedy brac go
   for v_opi in (select id,pra_id, od,opi_do from opieka where pio_id = 1 and umo_id = p_umowa_id order by 3 desc)  loop
     return (v_opi.id); 
   end loop;
 end if;

 if p_rok_umowy > 1 then
   for v_opi in (select id,pra_id, od,opi_do from opieka where umo_id = p_umowa_id order by 3)  loop
     if TRUNC(v_opi.od,'MM') <= p_data 
        and  (v_opi.opi_do >= ADD_MONTHS(trunc(p_data,'MM')-1,1) 
	or v_opi.opi_do IS NULL)      
       then return (v_opi.id); 
     end if;
   end loop;
   -- znajdz ostatniego opiekuna niealeznie od tego czy pracuje.
   for v_opi in (select id,pra_id, od,opi_do from opieka where umo_id = p_umowa_id order by 3 desc)  loop
     return (v_opi.id); 
   end loop;
 end if;



 return(null);
end;



/
