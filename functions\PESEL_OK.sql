--------------------------------------------------------
--  DDL for Function PESEL_OK
--------------------------------------------------------

  CREATE OR REPLACE FUNCTION "SUKCES"."PESEL_OK" 
  (p_pesel pracownicy.pesel%TYPE)
return boolean as
v_ok boolean;
v_lk number;
begin
  v_lk := 10-MOD(substr(p_pesel,1,1)*1
			+substr(p_pesel,2,1)*3
			+substr(p_pesel,3,1)*7
			+substr(p_pesel,4,1)*9
			+substr(p_pesel,5,1)*1
			+substr(p_pesel,6,1)*3
			+substr(p_pesel,7,1)*7
			+substr(p_pesel,8,1)*9
			+substr(p_pesel,9,1)*1
			+substr(p_pesel,10,1)*3,10);

  if v_lk = substr(p_pesel,11,1) then
    v_ok := true;
  elsif v_lk = 10 and substr(p_pesel,11,1) = 0 then
    v_ok := true;
  else 
    v_ok := false;
  end if;

return v_ok;
end;


/
