--------------------------------------------------------
--  DDL for Procedure BILANS_UMOW_SKL_OZNACZ_WPLATY
--------------------------------------------------------
set define off;

  CREATE OR REPLACE PROCEDURE "SUKCES"."BILANS_UMOW_SKL_OZNACZ_WPLATY" 
 (P_TOWARZYSTWO_ID VARCHAR
 ,P_ROK NUMBER
 ,P_MIES NUMBER
 ,P_SYMBOL_UMOWY VARCHAR2)
 AS
/*
Do przetestowania nie zastosowano jeszcze produkcyjnie 2020-4-30
<PERSON><PERSON>rane s� wszystkie zaznaczone jako sparowane wp�aty 
Zbierane s� wyszystkie zap�acone nale�no�ci
Wyliczny jest bilans = kwota wolna do spasowania

zaznaczne s� wszystkie nale�no�ci kt�re z tej wolnej kwoty mo�na przypisa� do wyp�acenia

*/
--JESLI ZAPLACONO WIECEJ NIZ JEDNA SKLADKE
--zmienne do kursora v_nal

    v_symbol_umowy	      naleznosci.symbol_umowy%type;
    v_zaplacono		        number;
    v_do_zaplaty	      	number;

    v_naleznosci_spasowane	      	number;
    v_wplaty_spasowane	      	number;
    v_wolna_kwota	      	number;
    v_pozostalo_wolne	      	number;

	v_jest                number;
	v_data_polisy         DATE;
	v_skladek     naleznosci.skladek%type; 
	V_ID_PRZETWARZANIA                number;

	p_ID                                 NUMBER;    
	p_UMOWA                              naleznosci.symbol_umowy%type;	
	p_BILANS_OTWARCIA_PROWIZJE			 NUMBER;	
	p_UMOWA_ID                           NUMBER;		
	p_BILANS_OTWARCIA_SKLADKI            NUMBER;	
	P_NAL_SKLADKI                 NUMBER;
	P_NAL_SKLADKI_ROZLICZONE      NUMBER;
    P_NAL_SKLADKI_WYPLACONE	     NUMBER;
	P_NAL_PROWIZJE                NUMBER;
	P_NAL_PROWIZJE_ROZLICZONE     NUMBER;	
	P_NAL_PROWIZJE_WYPLACONE      NUMBER;
    P_WPL_PROWIZJE				NUMBER	:=0;
	P_WPL_PROWIZJE_ROZLICZONE	NUMBER	:=0;        
	P_WPL_SKLADKI				NUMBER	:=0;
	P_WPL_SKLADKI_ROZLICZONE     NUMBER    :=0;

    p_BILANS_OTWARCIA_PROWIZJE_WPL NUMBER    :=0;
	p_BILANS_OTWARCIA_SKLADKI_WPL	NUMBER    :=0;
    p_data_wniosku  DATE;
    p_pra_id  NUMBER(10);




CURSOR c_wpl IS
SELECT distinct towarzystwo_id
      ,umowa
  FROM wplaty wpl
  WHERE rok_w > 2017
    and towarzystwo_id = P_TOWARZYSTWO_ID   
    and (P_SYMBOL_UMOWY IS NULL OR UMOWA = P_SYMBOL_UMOWY)
  ORDER BY UMOWA;

BEGIN
SELECT seq.nextval into V_ID_PRZETWARZANIA from dual;
INSERT INTO jakdlugo (SELECT TO_CHAR(SYSDATE, 'YY-MM-DD HH24:MI:SS') 
	||' Poczatek obliczania bilansu dla'||P_TOWARZYSTWO_ID||' za okres '||P_MIES||' '||P_ROK || ' ID='||V_ID_PRZETWARZANIA FROM dual);

if (P_ROK < 2017) then
    raise_application_error(-20001,'Proba wyliczenia bilansu dla lat wcze�niejszych ni� 2017');        
end if;
if (P_TOWARZYSTWO_ID <>'NATIONALE') then
    raise_application_error(-20002,'Proba wyliczenia bilansu dla innego niz NATIONALE - ta metod� oznaczania wplat opracowana jedynie dla Nationale');        
end if;

FOR v_wpl IN c_wpl LOOP

  BEGIN
    SELECT count(*) into v_jest from POLISY where symbol_umowy = v_wpl.umowa and towarzystwo_id = p_TOWARZYSTWO_ID; 
	IF V_JEST = 0 then
		INSERT INTO jakdlugo (SELECT TO_CHAR(SYSDATE, 'YY-MM-DD HH24:MI:SS') 
		||' B��d obliczania bilansu dla '||P_TOWARZYSTWO_ID||' za okres '||P_MIES||' '||P_ROK || ' umowa='||v_wpl.umowa || ' brak umowy'  FROM dual);	
		continue;
	END if;	
	IF V_JEST > 1 then
		INSERT INTO jakdlugo (SELECT TO_CHAR(SYSDATE, 'YY-MM-DD HH24:MI:SS') 
		||' B��d obliczania bilansu dla '||P_TOWARZYSTWO_ID||' za okres '||P_MIES||' '||P_ROK || ' umowa='||v_wpl.umowa || ' niejednoznaczna umowa  '||v_jest  FROM dual);	
		continue;
	END if;	
	SELECT UMO.id,data_wniosku into p_UMOWA_ID,p_data_wniosku from POLISY pol,umowy umo  where umo.pol_id = pol.id and pol.symbol_umowy = v_wpl.umowa and towarzystwo_id = p_TOWARZYSTWO_ID; 
    select pra_id into p_pra_id from opieka where umo_id= p_UMOWA_ID and opieka.opi_do is null and rownum <2;

	P_NAL_SKLADKI                :=0; 
	P_NAL_SKLADKI_ROZLICZONE     :=0;
	P_NAL_SKLADKI_WYPLACONE	    :=0;
	P_NAL_PROWIZJE               :=0;
	P_NAL_PROWIZJE_ROZLICZONE    :=0;
	P_NAL_PROWIZJE_WYPLACONE     :=0;
	P_WPL_PROWIZJE					:=0;
	P_WPL_PROWIZJE_ROZLICZONE		:=0;        
	P_WPL_SKLADKI					:=0;
	P_WPL_SKLADKI_ROZLICZONE         :=0;
	--naleznosci
    SELECT
		SUM(NVL(kwota,0)) --P_NAL_PROWIZJE
		,sum(NVL2(ROK_W, KWOTA, 0)) --P_NAL_PROWIZJE_ROZLICZONE
		,sum(NVL2(ROK_R, KWOTA, 0)) --P_NAL_PROWIZJE_WYPLACONE
		,SUM(NVL(kwota_SKLADKI,0)) --P_NAL_SKLADKI
		,sum(NVL2(ROK_W, kwota_SKLADKI, 0)) --P_NAL_SKLADKI_ROZLICZONE
		,sum(NVL2(ROK_R, kwota_SKLADKI, 0)) --P_NAL_SKLADKI_WYPLACONE		
		INTO
		  P_NAL_PROWIZJE
		, P_NAL_PROWIZJE_ROZLICZONE
		, P_NAL_PROWIZJE_WYPLACONE
		, P_NAL_SKLADKI
		, P_NAL_SKLADKI_ROZLICZONE
		, P_NAL_SKLADKI_WYPLACONE
		 FROM naleznosci nal 
    WHERE 
		 nal.symbol_umowy = v_wpl.umowa    
		 and rok_skladki =P_ROK	 
         and nal.miesiac_skladki <= p_mies
		 AND towarzystwo_id = P_TOWARZYSTWO_ID;
	-- bilans naleznosci	 
		SELECT 
  		 sum(NVL2(ROK_W, KWOTA, 0)) --,p_BILANS_OTWARCIA_PROWIZJE		
		,sum(NVL2(ROK_W, kwota_SKLADKI, 0)) --,p_BILANS_OTWARCIA_SKLADKI		
		INTO
		  p_BILANS_OTWARCIA_PROWIZJE
		, p_BILANS_OTWARCIA_SKLADKI		
		 FROM naleznosci nal 
    WHERE 
		 nal.symbol_umowy = v_wpl.umowa    
		 and rok_skladki < P_ROK	
         and rok_skladki >= 2017          
		 AND towarzystwo_id = P_TOWARZYSTWO_ID;


 --wplaty
        P_WPL_PROWIZJE:=0;
		P_WPL_PROWIZJE_ROZLICZONE:=0;
		P_WPL_SKLADKI:=0;
		P_WPL_SKLADKI_ROZLICZONE  :=0;
  SELECT  sum(nvl(PROWIZJA,0))
		 ,sum(NVL2(FLAG, PROWIZJA, 0))
         ,sum(nvl(KWOTA_SKLADKI,0))
		 ,sum(NVL2(FLAG, KWOTA_SKLADKI, 0))
  INTO 
        P_WPL_PROWIZJE,
		P_WPL_PROWIZJE_ROZLICZONE,        
		P_WPL_SKLADKI,
		P_WPL_SKLADKI_ROZLICZONE        
  FROM wplaty wpl
  WHERE     
	 umowa = v_wpl.umowa
	and rok_w =P_ROK	 
	AND towarzystwo_id = P_TOWARZYSTWO_ID;

	-- bilans
    
    p_BILANS_OTWARCIA_PROWIZJE_WPL :=0;
    p_BILANS_OTWARCIA_SKLADKI_WPL :=0;
   SELECT 
  		 SUM(NVL(PROWIZJA,0)) --,p_BILANS_OTWARCIA_PROWIZJE
		,SUM(NVL(KWOTA_SKLADKI,0)) --,p_BILANS_OTWARCIA_SKLADKI
		INTO
		 p_BILANS_OTWARCIA_PROWIZJE_WPL 
		, p_BILANS_OTWARCIA_SKLADKI_WPL
  FROM wplaty wpl
  WHERE     
	umowa = v_wpl.umowa
	and rok_w < P_ROK	
    and rok_w >= 2017
	AND towarzystwo_id = P_TOWARZYSTWO_ID;

	 p_BILANS_OTWARCIA_PROWIZJE := nvl(p_BILANS_OTWARCIA_PROWIZJE,0) - NVL(p_BILANS_OTWARCIA_PROWIZJE_WPL,0);
     p_BILANS_OTWARCIA_SKLADKI	:= nvl(p_BILANS_OTWARCIA_SKLADKI,0) -   NVL(p_BILANS_OTWARCIA_SKLADKI_WPL,0);


	--zapami�taj bilans
		select MAX(ID) into p_ID from UMOWA_BILANS where umowa = v_wpl.umowa and ROK = P_ROK and TOWARZYSTWO_ID = p_TOWARZYSTWO_ID; 
		if (p_ID is null) then 
				insert into UMOWA_BILANS(
				     TOWARZYSTWO_ID
					,UMOWA_ID
					,UMOWA
					,ROK
					,BILANS_OTWARCIA_PROWIZJE
					,WPLATY_PROWIZJE_ROZLICZONE					
					,NALEZNOSCI_SKLADKI_ROZLICZONE
					,WPLATY_SKLADKI									
					,BILANS_OTWARCIA_SKLADKI
					,NALEZNOSCI_PROWIZJE					
					,WPLATY_PROWIZJE
					,WPLATY_SKLADKI_ROZLICZONE
					,NALEZNOSCI_PROWIZJE_ROZLICZONE
					,NALEZNOSCI_SKLADKI
					,NALEZNOSCI_PROWIZJE_WYPLACONE
                    ,NALEZNOSCI_SKLADKI_WYPLACONE
                    ,DATA_WNIOSKU
                    ,PRA_ID
					) values (
					 p_TOWARZYSTWO_ID
					,p_UMOWA_ID
					,v_wpl.umowa
					,p_ROK
					,p_BILANS_OTWARCIA_PROWIZJE
					,P_WPL_PROWIZJE_ROZLICZONE					
					,P_NAL_SKLADKI_ROZLICZONE
					,P_WPL_SKLADKI							
					,p_BILANS_OTWARCIA_SKLADKI
					,P_NAL_PROWIZJE					
					,p_WPL_PROWIZJE
					,P_WPL_SKLADKI_ROZLICZONE
					,P_NAL_PROWIZJE_ROZLICZONE
					,P_NAL_SKLADKI
					,P_NAL_PROWIZJE_WYPLACONE
                    ,P_NAL_SKLADKI_WYPLACONE
                    ,p_data_wniosku
                    ,p_pra_id
				);
		else
			update UMOWA_BILANS set			        
					BILANS_OTWARCIA_PROWIZJE = p_BILANS_OTWARCIA_PROWIZJE
					,WPLATY_PROWIZJE_ROZLICZONE = P_WPL_PROWIZJE_ROZLICZONE					
					,NALEZNOSCI_SKLADKI_ROZLICZONE = P_NAL_SKLADKI_ROZLICZONE
					,WPLATY_SKLADKI = P_WPL_SKLADKI					
					,BILANS_OTWARCIA_SKLADKI = p_BILANS_OTWARCIA_SKLADKI
					,NALEZNOSCI_PROWIZJE = P_NAL_PROWIZJE					
					,WPLATY_PROWIZJE = p_WPL_PROWIZJE
					,WPLATY_SKLADKI_ROZLICZONE = P_WPL_SKLADKI_ROZLICZONE
					,NALEZNOSCI_PROWIZJE_ROZLICZONE = P_NAL_PROWIZJE_ROZLICZONE					
					,NALEZNOSCI_SKLADKI = P_NAL_SKLADKI
					,NALEZNOSCI_PROWIZJE_WYPLACONE = P_NAL_PROWIZJE_WYPLACONE
                    ,NALEZNOSCI_SKLADKI_WYPLACONE = P_NAL_SKLADKI_WYPLACONE
                    ,Pra_id = p_pra_id
                    ,DATA_WNIOSKU = p_data_wniosku
			where ID = p_ID;
		end if;


	--dopasowanie - potem doprogramowa�

	v_wolna_kwota :=  nvl(p_WPL_SKLADKI,0) - nvl(P_NAL_SKLADKI_ROZLICZONE,0) - nvl(p_BILANS_OTWARCIA_SKLADKI,0);

     IF (v_wolna_kwota >0 )        
        THEN
		   V_ZAPLACONO := v_wolna_kwota+ 0.10;
           v_naleznosci_spasowane :=0;
            FOR V_NAL IN (SELECT ROWID,KWOTA_SKLADKI 
                      FROM naleznosci nal
                     WHERE nal.symbol_umowy = v_wpl.umowa
                       AND rok_w IS NULL 
                       AND mies_w is null 
                       AND towarzystwo_id = p_TOWARZYSTWO_ID
                     ORDER BY ROK_SKLADKI, MIESIAC_SKLADKI)        							
    		LOOP
                IF V_NAL.KWOTA_SKLADKI <= V_ZAPLACONO THEN
    			  		V_ZAPLACONO := V_ZAPLACONO - V_NAL.KWOTA_SKLADKI;
    			  		v_naleznosci_spasowane := V_NAL.KWOTA_SKLADKI;
     				  UPDATE naleznosci
                            SET rok_w  = P_ROK ,mies_w = P_MIES,  
                            ID_DOPASOWANIA_WPL = V_ID_PRZETWARZANIA,
                            BILANS = v_wolna_kwota
                      WHERE rowid = V_NAL.ROWID;
                END IF;		
            END LOOP;

            v_pozostalo_wolne := v_wolna_kwota - v_naleznosci_spasowane;
            FOR V_WPL_SPASOWANE IN (SELECT ROWID,kwota_SKLADKI,
                        towarzystwo_id
                        ,umowa
                    FROM wplaty wpl
                    WHERE rok_w > 2017
                        and towarzystwo_id = P_TOWARZYSTWO_ID
                        and UMOWA = v_wpl.umowa
                        and (flag is null or flag = 'b')
                        ORDER BY rok_w desc,mies_w desc)
            LOOP
                IF v_pozostalo_wolne > 0 THEN
                    v_pozostalo_wolne := v_pozostalo_wolne - V_WPL_SPASOWANE.kwota_SKLADKI;
                    if v_pozostalo_wolne >= 0   then
                       Continue;
                    end if;
                    UPDATE wplaty
                         set  Flag ='b',
                             uwagi = uwagi || 'Metoda bilansowa po skladce: wp�ata cz�ciowo przypisana do nale�no�ci'
                    WHERE rowid = V_WPL_SPASOWANE.ROWID
                      and (flag is null or flag = 'b');
                else 
                      UPDATE wplaty
                           set  Flag ='b',
                           uwagi = uwagi || 'Metoda bilansowa po skladce: wp�ata ca�kowicie przypisana do nale�no�ci'
                      WHERE rowid = V_WPL_SPASOWANE.ROWID
                        and flag is null;
                END IF;
            END LOOP;


        END IF;

    EXCEPTION
    when others then

    INSERT INTO jakdlugo (SELECT TO_CHAR(SYSDATE, 'YY-MM-DD HH24:MI:SS') 
				||'BLAD UMOWY: '||v_wpl.umowa FROM dual);
  END; 
END LOOP;

INSERT INTO jakdlugo (SELECT TO_CHAR(SYSDATE, 'YY-MM-DD HH24:MI:SS') 
				||' Koniec obliczanie bilansu'||P_MIES||' '||P_ROK FROM dual);

 COMMIT;

END;

/
